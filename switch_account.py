"""
Flipkart Account Switcher
========================
Quick script to switch between different Flipkart accounts for scraping.
"""

import os
import json
import shutil
from datetime import datetime

def switch_account():
    """Switch to a new Flipkart account by clearing current cookies"""
    
    print("🔄 Flipkart Account Switcher")
    print("=" * 40)
    
    # Check if cookies exist
    cookies_file = "flipkart_cookies.json"
    
    if os.path.exists(cookies_file):
        # Backup current cookies
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"flipkart_cookies_backup_{timestamp}.json"
        
        try:
            shutil.copy2(cookies_file, backup_name)
            print(f"✅ Current cookies backed up to: {backup_name}")
        except Exception as e:
            print(f"⚠️ Warning: Could not backup cookies: {e}")
        
        # Remove current cookies
        try:
            os.remove(cookies_file)
            print(f"🗑️ Removed current cookies: {cookies_file}")
        except Exception as e:
            print(f"❌ Error removing cookies: {e}")
            return False
    else:
        print("ℹ️ No existing cookies found")
    
    print("\n🎯 Account switch prepared!")
    print("📋 Next steps:")
    print("   1. Run: python flipkart_complete_all_in_one_scraper.py")
    print("   2. The script will automatically detect no cookies")
    print("   3. Browser will open for manual login")
    print("   4. Login with your NEW account credentials")
    print("   5. Script will save new cookies and start scraping")
    
    print(f"\n💡 Your previous account cookies are safely backed up!")
    return True

def restore_account(backup_file=None):
    """Restore a previous account from backup"""
    
    print("🔄 Flipkart Account Restore")
    print("=" * 40)
    
    # Find backup files
    backup_files = [f for f in os.listdir('.') if f.startswith('flipkart_cookies_backup_')]
    
    if not backup_files:
        print("❌ No backup files found!")
        return False
    
    if backup_file is None:
        print("📁 Available backup files:")
        for i, file in enumerate(backup_files, 1):
            print(f"   {i}. {file}")
        
        try:
            choice = int(input("\nSelect backup to restore (number): ")) - 1
            if 0 <= choice < len(backup_files):
                backup_file = backup_files[choice]
            else:
                print("❌ Invalid choice!")
                return False
        except ValueError:
            print("❌ Invalid input!")
            return False
    
    # Restore the backup
    try:
        shutil.copy2(backup_file, "flipkart_cookies.json")
        print(f"✅ Restored cookies from: {backup_file}")
        print("🎯 You can now run the scraper with the restored account!")
        return True
    except Exception as e:
        print(f"❌ Error restoring backup: {e}")
        return False

def main():
    print("🔧 Flipkart Account Management")
    print("=" * 50)
    print("1. Switch to NEW account (clear current cookies)")
    print("2. Restore PREVIOUS account (from backup)")
    print("3. List all backup files")
    print("4. Exit")
    
    try:
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == "1":
            switch_account()
        elif choice == "2":
            restore_account()
        elif choice == "3":
            backup_files = [f for f in os.listdir('.') if f.startswith('flipkart_cookies_backup_')]
            if backup_files:
                print("\n📁 Available backup files:")
                for file in backup_files:
                    print(f"   📄 {file}")
            else:
                print("\n❌ No backup files found!")
        elif choice == "4":
            print("👋 Goodbye!")
        else:
            print("❌ Invalid choice!")
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")

if __name__ == "__main__":
    main()