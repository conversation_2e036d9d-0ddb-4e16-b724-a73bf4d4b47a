import asyncio
import json
import os
from playwright.async_api import async_playwright
import time

class FlipkartLogin:
    def __init__(self):
        self.cookies_file = "flipkart_cookies.json"
        self.login_url = "https://www.flipkart.com/account/login"
        self.home_url = "https://www.flipkart.com"
        
    async def save_cookies(self, context):
        """Save cookies to file"""
        cookies = await context.cookies()
        with open(self.cookies_file, 'w') as f:
            json.dump(cookies, f, indent=2)
        print("✅ Cookies saved successfully!")
    
    async def load_cookies(self, context):
        """Load cookies from file"""
        if os.path.exists(self.cookies_file):
            try:
                with open(self.cookies_file, 'r') as f:
                    cookies = json.load(f)
                await context.add_cookies(cookies)
                print("✅ Cookies loaded successfully!")
                return True
            except Exception as e:
                print(f"❌ Error loading cookies: {e}")
                return False
        return False
    
    async def check_login_status(self, page):
        """Check if user is logged in"""
        try:
            # Wait for page to load
            await page.wait_for_load_state('networkidle')
            await asyncio.sleep(2)  # Additional wait for dynamic content
            
            # Get current URL
            current_url = page.url
            print(f"🔍 Current URL: {current_url}")
            
            # If we're still on login page, definitely not logged in
            if "login" in current_url.lower():
                print("❌ User is not logged in (still on login page)")
                return False
            
            # Check for login indicators with more comprehensive selectors
            login_indicators = [
                # User account dropdown/menu
                '[data-testid="USER_MENU"]',
                'div[class*="exehdJ"]',  # Common Flipkart user menu class
                'span:has-text("Account")',
                'div:has-text("My Account")',
                
                # Profile/user name indicators
                'div[class*="profile"]',
                'span[class*="profile"]',
                
                # Navigation elements that appear after login
                'a[href*="/account/orders"]',
                'text="Orders"',
                'text="Wishlist"',
                
                # User greeting or name
                'div:has-text("Hello")',
                'span:has-text("Hello")',
                
                # Account related links
                'a[href*="/account"]',
                'a[title*="Account"]',
                
                # Logout option (indicates user is logged in)
                'text="Logout"',
                'div:has-text("Logout")'
            ]
            
            print("🔍 Checking for login indicators...")
            
            for i, indicator in enumerate(login_indicators):
                try:
                    element = await page.wait_for_selector(indicator, timeout=2000)
                    if element:
                        # Double check the element is visible
                        is_visible = await element.is_visible()
                        if is_visible:
                            print(f"✅ Login detected! Found indicator {i+1}: {indicator}")
                            return True
                except:
                    continue
            
            # Additional check: Look for any element containing user-specific text
            try:
                # Check page content for common post-login indicators
                page_content = await page.content()
                login_keywords = ['logout', 'my account', 'orders', 'wishlist', 'profile']
                
                for keyword in login_keywords:
                    if keyword.lower() in page_content.lower():
                        print(f"✅ Login detected! Found keyword: {keyword}")
                        return True
                        
            except Exception as e:
                print(f"⚠️ Error checking page content: {e}")
            
            # If we're not on login page and not on home page, might be logged in
            if not any(x in current_url.lower() for x in ['login', 'register', 'signup']):
                if 'flipkart.com' in current_url.lower():
                    print("✅ Likely logged in (navigated away from login page)")
                    return True
            
            print("❓ Login status unclear - no clear indicators found")
            return False
            
        except Exception as e:
            print(f"❌ Error checking login status: {e}")
            return False
    
    async def manual_login(self, page):
        """Handle manual login process"""
        print("\n🔐 Please complete the login process manually:")
        print("1. Enter your mobile number")
        print("2. Click 'Request OTP'")
        print("3. Enter the OTP you receive")
        print("4. Complete the login process")
        print("\n⏳ Waiting for you to complete login...")
        print("💡 Tip: The script will automatically detect when you're logged in!")
        
        # Wait for user to complete login
        login_completed = False
        max_wait_time = 300  # 5 minutes
        start_time = time.time()
        check_count = 0
        
        while not login_completed and (time.time() - start_time) < max_wait_time:
            await asyncio.sleep(3)  # Check every 3 seconds
            check_count += 1
            
            elapsed_time = int(time.time() - start_time)
            remaining_time = max_wait_time - elapsed_time
            
            print(f"⏱️ Check #{check_count} - {elapsed_time}s elapsed, {remaining_time}s remaining")
            
            # Check if login is completed
            if await self.check_login_status(page):
                login_completed = True
                print("🎉 Login completed successfully!")
                break
            
            # Give user feedback every 30 seconds
            if elapsed_time > 0 and elapsed_time % 30 == 0:
                print(f"⏳ Still waiting... {remaining_time} seconds remaining")
                print("💡 Make sure to complete the OTP verification!")
        
        if not login_completed:
            print("❌ Login timeout or failed")
            print("💡 Try running the script again if you need more time")
            return False
        
        return True
    
    async def debug_page_elements(self, page):
        """Debug function to see what elements are on the page"""
        try:
            print("\n🔍 DEBUG: Analyzing page elements...")
            
            # Get page title and URL
            title = await page.title()
            url = page.url
            print(f"📄 Page Title: {title}")
            print(f"🌐 Page URL: {url}")
            
            # Look for common elements
            debug_selectors = [
                'div[class*="header"]',
                'nav',
                'a[href*="account"]',
                'div[class*="user"]',
                'span:has-text("Account")',
                'div:has-text("My Account")',
                'text="Login"',
                'text="Logout"'
            ]
            
            for selector in debug_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"✅ Found {len(elements)} element(s) for: {selector}")
                        for i, element in enumerate(elements[:3]):  # Show first 3
                            text = await element.text_content()
                            if text and text.strip():
                                print(f"   └─ Element {i+1}: '{text.strip()[:50]}'")
                except:
                    pass
            
            print("🔍 DEBUG: Analysis complete\n")
            
        except Exception as e:
            print(f"❌ Debug error: {e}")
    
    async def run(self):
        """Main function to run the login automation"""
        async with async_playwright() as p:
            # Launch browser
            browser = await p.chromium.launch(
                headless=False,  # Keep browser visible for manual login
                args=['--no-sandbox', '--disable-blink-features=AutomationControlled']
            )
            
            # Create context
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            # Create page
            page = await context.new_page()
            
            try:
                # Try to load existing cookies
                cookies_loaded = await self.load_cookies(context)
                
                if cookies_loaded:
                    print("🔄 Attempting auto-login with saved cookies...")
                    
                    # Navigate to home page first
                    await page.goto(self.home_url)
                    await page.wait_for_load_state('networkidle')
                    
                    # Check if auto-login worked
                    if await self.check_login_status(page):
                        print("🎉 Auto-login successful!")
                        return True
                    else:
                        print("❌ Auto-login failed, proceeding with manual login...")
                
                # Navigate to login page for manual login
                print(f"🌐 Navigating to login page: {self.login_url}")
                await page.goto(self.login_url)
                
                # Perform manual login
                if await self.manual_login(page):
                    # Save cookies after successful login
                    await self.save_cookies(context)
                    print("🎉 Login process completed and cookies saved!")
                    
                    # Optional: Debug page elements after successful login
                    # await self.debug_page_elements(page)
                    
                    return True
                else:
                    print("❌ Login failed")
                    # Debug what's on the page when login fails
                    await self.debug_page_elements(page)
                    return False
                    
            except Exception as e:
                print(f"❌ Error during login process: {e}")
                return False
            
            finally:
                # Keep browser open for a moment to see the result
                print("\n⏳ Keeping browser open for 10 seconds...")
                await asyncio.sleep(10)
                await browser.close()

async def main():
    """Main entry point"""
    print("🚀 Starting Flipkart Login Automation")
    print("=" * 50)
    
    login_bot = FlipkartLogin()
    success = await login_bot.run()
    
    if success:
        print("\n✅ Script completed successfully!")
    else:
        print("\n❌ Script failed!")

if __name__ == "__main__":
    asyncio.run(main())