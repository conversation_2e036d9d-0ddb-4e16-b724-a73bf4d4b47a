"""
Flipkart Complete All-in-One Order Scraper
==========================================
A comprehensive script that handles everything from login to complete data extraction.

Features:
- Automatic login with saved cookies (if available)
- Manual login option if cookies are invalid
- Complete API-based order scraping
- Extracts ALL order details: prices, discounts, delivery dates, tracking IDs, etc.
- Saves results to JSON file
- Single file solution - no dependencies on other files

Author: AI Assistant
Date: 2025-06-01
"""

import asyncio
import json
import time
import aiohttp
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests

class FlipkartCompleteAllInOneScraper:
    def __init__(self):
        self.base_api_url = "https://1.rome.api.flipkart.com/api/5/self-serve/orders/"
        self.login_url = "https://www.flipkart.com/account/login"
        self.orders_url = "https://www.flipkart.com/my-orders"
        
        self.orders_data = []
        self.scraping_start_time = None
        self.session = None
        self.processed_order_ids = set()
        
        # Cookies and API template
        self.cookies = {}
        self.api_template = {
            "required_headers": {
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "en-US,en;q=0.9",
                "Cache-Control": "no-cache",
                "Content-Type": "application/json",
                "DNT": "1",
                "Origin": "https://www.flipkart.com",
                "Pragma": "no-cache",
                "Referer": "https://www.flipkart.com/",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-site",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "X-User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 FKUA/website/42/website/Desktop"
            },
            "required_cookies": {}
        }
    
    def load_saved_cookies(self):
        """Load cookies from file if available"""
        try:
            if os.path.exists('flipkart_cookies.json'):
                with open('flipkart_cookies.json', 'r') as f:
                    cookies_data = json.load(f)
                
                self.cookies = {}
                for cookie in cookies_data:
                    self.cookies[cookie['name']] = cookie['value']
                
                print("✅ Loaded saved cookies")
                return True
            else:
                print("⚠️ No saved cookies found")
                return False
        except Exception as e:
            print(f"❌ Error loading cookies: {e}")
            return False
    
    def save_cookies(self, driver_cookies):
        """Save cookies to file"""
        try:
            with open('flipkart_cookies.json', 'w') as f:
                json.dump(driver_cookies, f, indent=2)
            print("✅ Cookies saved successfully")
        except Exception as e:
            print(f"❌ Error saving cookies: {e}")
    
    async def test_cookies_validity(self):
        """Test if saved cookies are still valid"""
        try:
            if not self.cookies:
                print("❌ No cookies to test")
                return False
            
            headers = self.api_template['required_headers'].copy()
            
            connector = aiohttp.TCPConnector(ssl=False)
            session = aiohttp.ClientSession(
                headers=headers,
                cookies=self.cookies,
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test with a simple API call
            params = {'page': 1, 'filterType': 'PREORDER_UNITS'}
            
            print("🔍 Testing cookies validity with API call...")
            async with session.get(self.base_api_url, params=params) as response:
                print(f"📡 API Response Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    if 'RESPONSE' in data and 'multipleOrderDetailsView' in data['RESPONSE']:
                        print("✅ Saved cookies are valid and working!")
                        await session.close()
                        return True
                    else:
                        print("❌ API response structure invalid - cookies may be expired")
                elif response.status == 401:
                    print("❌ Unauthorized - cookies are expired")
                elif response.status == 403:
                    print("❌ Forbidden - cookies are invalid")
                else:
                    print(f"❌ API call failed with status {response.status}")
                
                await session.close()
                return False
                
        except Exception as e:
            print(f"❌ Error testing cookies: {e}")
            if 'session' in locals():
                await session.close()
            return False
    
    def setup_driver(self):
        """Setup Chrome driver for login"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return driver
        except Exception as e:
            print(f"❌ Error setting up Chrome driver: {e}")
            print("💡 Make sure ChromeDriver is installed and in PATH")
            return None
    
    def perform_manual_login(self):
        """Perform manual login using Selenium"""
        print("🔐 Starting manual login process...")
        
        driver = self.setup_driver()
        if not driver:
            return False
        
        try:
            # Navigate to login page
            print("📱 Opening Flipkart login page...")
            driver.get(self.login_url)
            
            # Wait for page to load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            print("👤 Please complete the login manually in the browser window:")
            print("   1. Enter your mobile number/email")
            print("   2. Enter your password")
            print("   3. Complete any captcha if required")
            print("   4. Click Login")
            print("   5. Wait for the page to redirect to your account")
            
            # Wait for user to complete login
            print("\n⏳ Waiting for login completion...")
            print("   The script will automatically detect when you're logged in")
            
            # Check for successful login by waiting for orders page or account page
            login_successful = False
            max_wait_time = 300  # 5 minutes
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                current_url = driver.current_url
                
                # Check if we're on account page, orders page, or main page (logged in)
                if any(indicator in current_url.lower() for indicator in [
                    '/account/', '/my-orders', '/my-account', 'flipkart.com/?'
                ]) or 'login' not in current_url.lower():
                    
                    # Additional check: look for user-specific elements
                    try:
                        # Try to find account-related elements
                        account_elements = driver.find_elements(By.XPATH, 
                            "//div[contains(@class, 'account') or contains(@class, 'user') or contains(@class, 'profile')]")
                        
                        if account_elements or 'login' not in current_url.lower():
                            login_successful = True
                            break
                    except:
                        pass
                
                time.sleep(2)
                print(".", end="", flush=True)
            
            if login_successful:
                print(f"\n✅ Login successful! Current URL: {driver.current_url}")
                
                # Navigate to orders page to ensure we have the right cookies
                print("📦 Navigating to orders page...")
                driver.get(self.orders_url)
                time.sleep(3)
                
                # Get cookies
                cookies = driver.get_cookies()
                self.save_cookies(cookies)
                
                # Update internal cookies
                self.cookies = {}
                for cookie in cookies:
                    self.cookies[cookie['name']] = cookie['value']
                
                driver.quit()
                return True
            else:
                print(f"\n❌ Login timeout after {max_wait_time} seconds")
                driver.quit()
                return False
                
        except Exception as e:
            print(f"\n❌ Error during manual login: {e}")
            if driver:
                driver.quit()
            return False
    
    async def fetch_orders_page(self, page=1, order_before_timestamp=None):
        """Fetch orders from API"""
        try:
            params = {
                'page': page,
                'filterType': 'PREORDER_UNITS'
            }
            
            if order_before_timestamp:
                current_timestamp = int(time.time() * 1000)
                params['order_before_time_stamp'] = order_before_timestamp
                params['st'] = current_timestamp
                params['ot'] = order_before_timestamp
            
            print(f"📡 Fetching page {page} from API...")
            
            async with self.session.get(self.base_api_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Successfully fetched page {page}")
                    return data
                else:
                    print(f"❌ API request failed with status {response.status}")
                    return None
        except Exception as e:
            print(f"❌ Error fetching page {page}: {e}")
            return None
    
    def extract_order_data_from_api(self, api_response):
        """Extract order data with ALL COMPLETE details"""
        try:
            orders = []
            
            if not isinstance(api_response, dict) or 'RESPONSE' not in api_response:
                return []
            
            response_data = api_response['RESPONSE']
            if 'multipleOrderDetailsView' not in response_data:
                return []
            
            order_view = response_data['multipleOrderDetailsView']
            if 'orders' not in order_view or not isinstance(order_view['orders'], list):
                return []
            
            orders_list = order_view['orders']
            print(f"🔍 Found {len(orders_list)} order objects in API response")
            
            for order_obj in orders_list:
                if not isinstance(order_obj, dict) or 'orderMetaData' not in order_obj:
                    continue
                
                metadata = order_obj['orderMetaData']
                order_id = metadata.get('orderId')
                
                # Skip duplicates
                if order_id in self.processed_order_ids:
                    print(f"   ⏭️ Skipping duplicate order: {order_id}")
                    continue
                
                # Extract the order with ALL COMPLETE details
                extracted_order = self.extract_complete_order_with_all_details(order_obj)
                if extracted_order:
                    orders.append(extracted_order)
                    self.processed_order_ids.add(order_id)
                    print(f"   ✅ Extracted order: {order_id} with {len(extracted_order.get('products', []))} products")
            
            print(f"✅ Total NEW orders extracted: {len(orders)}")
            return orders
            
        except Exception as e:
            print(f"❌ Error extracting order data: {e}")
            return []
    
    def extract_complete_order_with_all_details(self, order_obj):
        """Extract complete order data including ALL missing details"""
        try:
            metadata = order_obj['orderMetaData']
            
            # Basic order information
            order = {
                'order_id': metadata.get('orderId', 'N/A'),
                'order_date': self.convert_timestamp(metadata.get('orderDate')),
                'number_of_items': metadata.get('numberOfItems', 0),
                'order_status': 'N/A',
                'marketplace': metadata.get('orderMarketPlaceMeta', 'N/A'),
                'products': [],
                
                # COMPLETE FINANCIAL DETAILS
                'total_amount': 'N/A',
                'currency': 'INR',
                'payment_method': 'N/A',
                'payment_status': 'N/A',
                
                # COMPLETE DELIVERY DETAILS
                'delivery_address': 'N/A',
                'delivery_date': 'N/A',
                'delivery_status': 'N/A',
                'delivery_message': 'N/A',
                'estimated_delivery': 'N/A',
                'tracking_id': 'N/A',
                
                # ADDITIONAL ORDER DETAILS
                'order_type': 'N/A',
                'seller_details': [],
                'offers_applied': [],
                'savings_amount': 'N/A',
                'shipping_charges': 'N/A',
                'tax_details': 'N/A',
                
                # METADATA
                'extracted_at': datetime.now().isoformat(),
                'api_version': 'complete_all_in_one_v1'
            }
            
            # Extract COMPLETE financial information from orderMoneyDataBag
            if 'orderMoneyDataBag' in order_obj:
                money_data = order_obj['orderMoneyDataBag']
                order.update(self.extract_complete_financial_details(money_data))
            
            # Get product data bag for product details
            product_data_bag = order_obj.get('productDataBag', {})
            
            # Extract products from units with COMPLETE details
            if 'units' in order_obj and isinstance(order_obj['units'], dict):
                units_dict = order_obj['units']
                print(f"     🛍️ Processing {len(units_dict)} units for order {order['order_id']}")
                
                for unit_id, unit_data in units_dict.items():
                    product = self.extract_complete_product_with_all_details(unit_data, unit_id, product_data_bag)
                    if product:
                        order['products'].append(product)
            
            # Extract COMPLETE delivery information from unitProperties
            if 'unitProperties' in order_obj:
                delivery_info = self.extract_complete_delivery_details(order_obj['unitProperties'])
                order.update(delivery_info)
            
            # Extract order status from first product or unit data
            if order['products']:
                order['order_status'] = order['products'][0].get('status', 'N/A')
                order['delivery_date'] = order['products'][0].get('delivery_date', 'N/A')
                order['tracking_id'] = order['products'][0].get('tracking_id', 'N/A')
            
            # Extract seller details
            if 'sellerDataBag' in order_obj:
                order['seller_details'] = self.extract_seller_details(order_obj['sellerDataBag'])
            
            return order
            
        except Exception as e:
            print(f"⚠️ Error extracting complete order: {e}")
            return None
    
    def extract_complete_financial_details(self, money_data):
        """Extract ALL financial details from orderMoneyDataBag"""
        financial_details = {}
        
        try:
            if isinstance(money_data, dict):
                # Total amount
                if 'amount' in money_data:
                    financial_details['total_amount'] = f"₹{money_data['amount']}"
                
                # Currency details
                if 'amountValueMultiCurrency' in money_data and isinstance(money_data['amountValueMultiCurrency'], list):
                    for currency_info in money_data['amountValueMultiCurrency']:
                        if isinstance(currency_info, dict) and 'currency' in currency_info:
                            financial_details['currency'] = currency_info['currency']
                            break
                
                # Payment methods
                if 'paymentMethods' in money_data and isinstance(money_data['paymentMethods'], list):
                    financial_details['payment_method'] = ', '.join(money_data['paymentMethods'])
                
                # Savings
                if 'totalSavings' in money_data:
                    financial_details['savings_amount'] = f"₹{money_data['totalSavings']}"
                
                # Offers
                if 'offerDetails' in money_data and isinstance(money_data['offerDetails'], list):
                    offers = []
                    for offer in money_data['offerDetails']:
                        if isinstance(offer, dict) and 'offerText' in offer:
                            offers.append(offer['offerText'])
                    financial_details['offers_applied'] = offers
                
                # Payment transactions
                if 'paymentTransactions' in money_data and isinstance(money_data['paymentTransactions'], list):
                    if money_data['paymentTransactions']:
                        financial_details['payment_status'] = 'Completed'
                    else:
                        financial_details['payment_status'] = 'Pending'
        
        except Exception as e:
            print(f"⚠️ Error extracting financial details: {e}")
        
        return financial_details
    
    def extract_complete_delivery_details(self, unit_properties):
        """Extract ALL delivery details from unitProperties"""
        delivery_details = {}
        
        try:
            if isinstance(unit_properties, list) and unit_properties:
                first_unit_prop = unit_properties[0]
                
                if isinstance(first_unit_prop, dict):
                    # Delivery message
                    if 'deliveryMessage' in first_unit_prop:
                        delivery_details['delivery_message'] = first_unit_prop['deliveryMessage']
                        
                        # Extract delivery date from message
                        delivery_msg = first_unit_prop['deliveryMessage']
                        if 'Delivered on' in delivery_msg:
                            delivery_details['delivery_date'] = delivery_msg.replace('Delivered on ', '')
                            delivery_details['delivery_status'] = 'Delivered'
                        elif 'Expected by' in delivery_msg:
                            delivery_details['estimated_delivery'] = delivery_msg.replace('Expected by ', '')
                            delivery_details['delivery_status'] = 'In Transit'
                    
                    # Additional delivery info
                    if 'moRedesignHeading' in first_unit_prop:
                        delivery_details['delivery_status_heading'] = first_unit_prop['moRedesignHeading']
        
        except Exception as e:
            print(f"⚠️ Error extracting delivery details: {e}")
        
        return delivery_details
    
    def extract_complete_product_with_all_details(self, unit_data, unit_id, product_data_bag):
        """Extract complete product information with ALL details"""
        try:
            product = {
                'unit_id': unit_id,
                'product_name': 'N/A',
                'product_image': 'N/A',
                'price': 'N/A',
                'original_price': 'N/A',
                'discount': 'N/A',
                'quantity': 1,
                'seller': 'N/A',
                'seller_id': 'N/A',
                'product_url': 'N/A',
                'status': 'N/A',
                'brand': 'N/A',
                'category': 'N/A',
                'fsn': 'N/A',
                'tracking_id': 'N/A',
                'delivery_date': 'N/A',
                'delivery_message': 'N/A',
                'return_policy': 'N/A',
                'warranty': 'N/A'
            }
            
            # Extract basic info from unit data
            if isinstance(unit_data, dict):
                # Extract from metaData
                if 'metaData' in unit_data and isinstance(unit_data['metaData'], dict):
                    metadata = unit_data['metaData']
                    
                    # Product name
                    if 'title' in metadata and metadata['title']:
                        product['product_name'] = metadata['title']
                    
                    # Get listing ID from metaData
                    listing_id = metadata.get('listingId')
                    
                    # Other metadata fields
                    if 'quantity' in metadata:
                        product['quantity'] = metadata['quantity']
                    
                    if 'sellerId' in metadata:
                        product['seller_id'] = metadata['sellerId']
                    
                    if 'fsn' in metadata:
                        product['fsn'] = metadata['fsn']
                    
                    if 'trackingId' in metadata:
                        product['tracking_id'] = metadata['trackingId']
                    
                    # Extract status from metaData
                    if 'status' in metadata and isinstance(metadata['status'], dict):
                        status_data = metadata['status']
                        if 'text' in status_data:
                            product['status'] = status_data['text']
                        elif 'key' in status_data:
                            product['status'] = status_data['key']
                else:
                    listing_id = unit_data.get('listingId')
                
                # Extract COMPLETE price information from moneyDataBag
                if 'moneyDataBag' in unit_data and isinstance(unit_data['moneyDataBag'], dict):
                    money_bag = unit_data['moneyDataBag']
                    
                    # Selling price
                    if 'itemSellingPrice' in money_bag:
                        product['price'] = f"₹{money_bag['itemSellingPrice']}"
                    elif 'sellingPrice' in money_bag:
                        product['price'] = f"₹{money_bag['sellingPrice']}"
                    
                    # Original price
                    if 'itemListingPrice' in money_bag:
                        product['original_price'] = f"₹{money_bag['itemListingPrice']}"
                    elif 'listPrice' in money_bag:
                        product['original_price'] = f"₹{money_bag['listPrice']}"
                    
                    # Calculate discount
                    if 'itemListingPrice' in money_bag and 'itemSellingPrice' in money_bag:
                        original = money_bag['itemListingPrice']
                        selling = money_bag['itemSellingPrice']
                        if original > selling:
                            discount_amount = original - selling
                            discount_percent = round((discount_amount / original) * 100, 1)
                            product['discount'] = f"₹{discount_amount} ({discount_percent}% off)"
                
                # Extract delivery information from deliveryDataBag
                if 'deliveryDataBag' in unit_data and isinstance(unit_data['deliveryDataBag'], dict):
                    delivery_bag = unit_data['deliveryDataBag']
                    
                    if 'promiseDataBag' in delivery_bag and isinstance(delivery_bag['promiseDataBag'], dict):
                        promise_data = delivery_bag['promiseDataBag']
                        
                        if 'deliveryMessage' in promise_data:
                            product['delivery_message'] = promise_data['deliveryMessage']
                            
                            # Extract delivery date from message
                            delivery_msg = promise_data['deliveryMessage']
                            if 'Delivered on' in delivery_msg:
                                product['delivery_date'] = delivery_msg.replace('Delivered on ', '')
                
                # Get detailed product info from productDataBag using listingId
                if listing_id and listing_id in product_data_bag:
                    product_details = product_data_bag[listing_id]
                    
                    # Extract additional product details
                    if 'productBasicData' in product_details:
                        basic_data = product_details['productBasicData']
                        
                        # If we didn't get title from unit data, try from productBasicData
                        if product['product_name'] == 'N/A' and 'title' in basic_data:
                            product['product_name'] = basic_data['title']
                        
                        # Product URL
                        if 'url' in basic_data:
                            product['product_url'] = f"https://www.flipkart.com{basic_data['url']}"
                        
                        # Product image
                        if 'imageLocation' in basic_data and isinstance(basic_data['imageLocation'], dict):
                            # Get the best quality image
                            image_sizes = ['400x400', '200x200', '125x125', '100x100']
                            for size in image_sizes:
                                if size in basic_data['imageLocation']:
                                    image_url = basic_data['imageLocation'][size]
                                    # Replace placeholders with actual values
                                    image_url = image_url.replace('{@width}', '400').replace('{@height}', '400').replace('{@quality}', '70')
                                    product['product_image'] = image_url
                                    break
                        
                        # Category
                        if 'category' in basic_data:
                            product['category'] = basic_data['category']
                        
                        # Vertical (additional category info)
                        if 'vertical' in basic_data:
                            if product['category'] != 'N/A':
                                product['category'] = f"{product['category']} > {basic_data['vertical']}"
                            else:
                                product['category'] = basic_data['vertical']
                    
                    # Extract brand and other attributes
                    if 'productAttribute' in product_details:
                        attr_data = product_details['productAttribute']
                        
                        if 'brand' in attr_data and attr_data['brand']:
                            product['brand'] = attr_data['brand']
                        
                        # Add color and size info to product name if available
                        if 'color' in attr_data and attr_data['color']:
                            if product['product_name'] != 'N/A':
                                product['product_name'] += f" ({attr_data['color']})"
                        
                        if 'size' in attr_data and attr_data['size']:
                            if product['product_name'] != 'N/A':
                                product['product_name'] += f" - {attr_data['size']}"
            
            return product
            
        except Exception as e:
            print(f"⚠️ Error extracting complete product from unit {unit_id}: {e}")
            return None
    
    def extract_seller_details(self, seller_data_bag):
        """Extract seller details"""
        try:
            if isinstance(seller_data_bag, dict) and 'sellerDetails' in seller_data_bag:
                return seller_data_bag['sellerDetails']
            return {}
        except:
            return {}
    
    def convert_timestamp(self, timestamp):
        """Convert timestamp to readable date"""
        try:
            if timestamp:
                timestamp_seconds = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp_seconds)
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            return 'N/A'
        except:
            return 'N/A'
    
    def get_next_page_timestamp(self, current_page_data):
        """Get timestamp for next page"""
        try:
            if not isinstance(current_page_data, dict) or 'RESPONSE' not in current_page_data:
                return None
            
            response_data = current_page_data['RESPONSE']
            if 'multipleOrderDetailsView' not in response_data:
                return None
            
            order_view = response_data['multipleOrderDetailsView']
            if 'orders' not in order_view or not isinstance(order_view['orders'], list):
                return None
            
            orders_list = order_view['orders']
            if not orders_list:
                return None
            
            # Get the oldest order timestamp
            min_timestamp = None
            for order_obj in orders_list:
                if isinstance(order_obj, dict) and 'orderMetaData' in order_obj:
                    metadata = order_obj['orderMetaData']
                    order_date = metadata.get('orderDate')
                    if order_date:
                        if min_timestamp is None or order_date < min_timestamp:
                            min_timestamp = order_date
            
            return min_timestamp
        except Exception as e:
            print(f"⚠️ Error getting next page timestamp: {e}")
            return None
    
    async def save_orders_json(self, orders):
        """Save orders to JSON file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"flipkart_complete_orders_{timestamp}.json"
            
            # Create summary
            summary = {
                "scraping_info": {
                    "total_orders": len(orders),
                    "total_products": sum(len(order.get('products', [])) for order in orders),
                    "scraped_at": datetime.now().isoformat(),
                    "scraping_duration_seconds": round(time.time() - self.scraping_start_time, 2) if self.scraping_start_time else 0,
                    "scraping_method": "complete_all_in_one_api_scraper",
                    "api_endpoint": self.base_api_url,
                    "average_time_per_order": round((time.time() - self.scraping_start_time) / len(orders), 4) if self.scraping_start_time and orders else 0,
                    "unique_orders_processed": len(self.processed_order_ids),
                    "extracted_fields": [
                        "order_id", "order_date", "total_amount", "payment_method", "delivery_date",
                        "delivery_status", "tracking_id", "product_names", "product_images", "product_urls",
                        "brands", "categories", "prices", "discounts", "seller_details", "offers_applied"
                    ]
                },
                "orders": orders
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Orders saved to: {filename}")
            return filename
        except Exception as e:
            print(f"❌ Error saving orders: {e}")
            return None
    
    async def run_complete_scraping(self, max_pages=None):
        """Main complete scraping function"""
        try:
            self.scraping_start_time = time.time()
            
            print("🚀 Flipkart Complete All-in-One Order Scraper")
            print("=" * 60)
            print("🔧 Handles everything from login to complete data extraction")
            
            # Step 1: Try to load and test saved cookies
            print("\n📋 Step 1: Checking saved cookies...")
            cookies_loaded = self.load_saved_cookies()
            
            if cookies_loaded:
                cookies_valid = await self.test_cookies_validity()
                if not cookies_valid:
                    print("🔄 Saved cookies are invalid, need to login again")
                    cookies_loaded = False
            
            # Step 2: Perform login if needed
            if not cookies_loaded:
                print("\n📋 Step 2: Performing login...")
                login_success = self.perform_manual_login()
                if not login_success:
                    print("❌ Login failed, cannot proceed")
                    return False
                print("✅ Login completed successfully")
            else:
                print("✅ Using saved cookies, skipping login")
            
            # Step 3: Setup API session
            print("\n📋 Step 3: Setting up API session...")
            headers = self.api_template['required_headers'].copy()
            
            connector = aiohttp.TCPConnector(ssl=False)
            self.session = aiohttp.ClientSession(
                headers=headers,
                cookies=self.cookies,
                connector=connector
            )
            
            print("📡 Starting complete API scraping...")
            
            # Step 4: Scrape orders
            all_orders = []
            page = 1
            next_timestamp = None
            
            while True:
                if max_pages and page > max_pages:
                    print(f"🛑 Reached page limit: {max_pages}")
                    break
                
                # Fetch page data
                page_data = await self.fetch_orders_page(page, next_timestamp)
                if not page_data:
                    print(f"❌ Failed to fetch page {page}")
                    break
                
                # Extract orders
                page_orders = self.extract_order_data_from_api(page_data)
                if not page_orders:
                    print(f"📄 No NEW orders found on page {page}")
                    break
                
                all_orders.extend(page_orders)
                print(f"📦 Page {page}: Found {len(page_orders)} NEW orders (Total unique: {len(all_orders)})")
                
                # Get next page timestamp
                next_timestamp = self.get_next_page_timestamp(page_data)
                if not next_timestamp:
                    print("📄 No more pages available")
                    break
                
                page += 1
                
                # Progress update
                if page % 3 == 0:
                    elapsed = time.time() - self.scraping_start_time
                    rate = len(all_orders) / elapsed if elapsed > 0 else 0
                    print(f"⏱️ Progress: {len(all_orders)} unique orders in {elapsed:.1f}s ({rate:.1f} orders/sec)")
                
                await asyncio.sleep(0.5)
            
            # Step 5: Save results
            if all_orders:
                filename = await self.save_orders_json(all_orders)
                
                # Print summary
                actual_time = round(time.time() - self.scraping_start_time, 2)
                total_products = sum(len(order.get('products', [])) for order in all_orders)
                
                print(f"\n🎉 COMPLETE ALL-IN-ONE scraping completed successfully!")
                print(f"📊 Total UNIQUE orders scraped: {len(all_orders)}")
                print(f"🛍️ Total products found: {total_products}")
                print(f"📄 Pages processed: {page - 1}")
                print(f"⏱️ Total time: {actual_time:.1f} seconds")
                print(f"⚡ Speed: {len(all_orders)/actual_time:.1f} orders/second")
                print(f"💾 Data saved to: {filename}")
                
                # Show sample with COMPLETE data
                if all_orders:
                    sample = all_orders[0]
                    print(f"\n📋 Sample order with ALL COMPLETE details:")
                    print(f"   Order ID: {sample['order_id']}")
                    print(f"   Date: {sample['order_date']}")
                    print(f"   Total Amount: {sample['total_amount']}")
                    print(f"   Payment: {sample['payment_method']}")
                    print(f"   Delivery Date: {sample['delivery_date']}")
                    print(f"   Delivery Status: {sample['delivery_status']}")
                    print(f"   Tracking ID: {sample['tracking_id']}")
                    print(f"   Savings: {sample['savings_amount']}")
                    print(f"   Products: {len(sample.get('products', []))}")
                    if sample.get('products'):
                        first_product = sample['products'][0]
                        print(f"   First product: {first_product.get('product_name', 'N/A')}")
                        print(f"   Price: {first_product.get('price', 'N/A')}")
                        print(f"   Original Price: {first_product.get('original_price', 'N/A')}")
                        print(f"   Discount: {first_product.get('discount', 'N/A')}")
                        print(f"   Brand: {first_product.get('brand', 'N/A')}")
                        print(f"   Delivery: {first_product.get('delivery_date', 'N/A')}")
                
                return True
            else:
                print("❌ No orders extracted")
                return False
                
        except Exception as e:
            print(f"❌ Error during complete scraping: {e}")
            return False
        
        finally:
            if self.session:
                await self.session.close()

async def main():
    print("🔧 Flipkart Complete All-in-One Order Scraper")
    print("🎯 Single script solution: Login → API Scraping → Complete Data Extraction")
    print("=" * 80)
    
    try:
        choice = input("How many pages to scrape? (Enter number or 'all'): ").strip()
        
        max_pages = None
        if choice.lower() != 'all':
            try:
                max_pages = int(choice)
            except:
                max_pages = 5
                print(f"Invalid input, using {max_pages} pages for testing")
        
        scraper = FlipkartCompleteAllInOneScraper()
        success = await scraper.run_complete_scraping(max_pages)
        
        if success:
            print("\n✅ COMPLETE ALL-IN-ONE scraping finished successfully!")
            print("📁 Check the JSON file for ALL order and product details!")
            print("🎯 This single script handled everything from login to data extraction!")
        else:
            print("\n❌ COMPLETE ALL-IN-ONE scraping failed!")
            
    except KeyboardInterrupt:
        print("\n❌ Scraping cancelled by user")

if __name__ == "__main__":
    asyncio.run(main())