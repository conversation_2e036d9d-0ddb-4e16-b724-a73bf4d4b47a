import asyncio
import json
import time
import aiohttp
from datetime import datetime
from flipkart_login import FlipkartLogin

class FlipkartCompleteDetailsScraper(FlipkartLogin):
    def __init__(self):
        super().__init__()
        self.base_api_url = "https://1.rome.api.flipkart.com/api/5/self-serve/orders/"
        self.orders_data = []
        self.scraping_start_time = None
        self.session = None
        self.api_template = None
        self.processed_order_ids = set()
        
    async def load_api_template(self):
        """Load the captured API template"""
        try:
            with open('flipkart_api_template.json', 'r') as f:
                self.api_template = json.load(f)
            return True
        except Exception as e:
            print(f"❌ Error loading API template: {e}")
            return False
    
    async def get_current_cookies(self):
        """Get current cookies"""
        try:
            with open('flipkart_cookies.json', 'r') as f:
                current_cookies_data = json.load(f)
            
            current_cookies = {}
            for cookie in current_cookies_data:
                current_cookies[cookie['name']] = cookie['value']
            
            template_cookies = self.api_template['required_cookies']
            final_cookies = current_cookies.copy()
            final_cookies.update(template_cookies)
            
            return final_cookies
        except Exception as e:
            print(f"❌ Error loading cookies: {e}")
            return self.api_template['required_cookies']
    
    async def get_exact_headers(self):
        """Get exact headers"""
        headers = self.api_template['required_headers'].copy()
        headers.update({
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'DNT': '1',
            'Origin': 'https://www.flipkart.com',
            'Pragma': 'no-cache',
            'Referer': 'https://www.flipkart.com/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        return headers
    
    async def fetch_orders_page(self, page=1, order_before_timestamp=None):
        """Fetch orders from API"""
        try:
            params = {
                'page': page,
                'filterType': 'PREORDER_UNITS'
            }
            
            if order_before_timestamp:
                current_timestamp = int(time.time() * 1000)
                params['order_before_time_stamp'] = order_before_timestamp
                params['st'] = current_timestamp
                params['ot'] = order_before_timestamp
            
            print(f"📡 Fetching page {page} from API...")
            
            async with self.session.get(self.base_api_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Successfully fetched page {page}")
                    return data
                else:
                    print(f"❌ API request failed with status {response.status}")
                    return None
        except Exception as e:
            print(f"❌ Error fetching page {page}: {e}")
            return None
    
    def extract_order_data_from_api(self, api_response):
        """Extract order data with ALL COMPLETE details"""
        try:
            orders = []
            
            if not isinstance(api_response, dict) or 'RESPONSE' not in api_response:
                return []
            
            response_data = api_response['RESPONSE']
            if 'multipleOrderDetailsView' not in response_data:
                return []
            
            order_view = response_data['multipleOrderDetailsView']
            if 'orders' not in order_view or not isinstance(order_view['orders'], list):
                return []
            
            orders_list = order_view['orders']
            print(f"🔍 Found {len(orders_list)} order objects in API response")
            
            for order_obj in orders_list:
                if not isinstance(order_obj, dict) or 'orderMetaData' not in order_obj:
                    continue
                
                metadata = order_obj['orderMetaData']
                order_id = metadata.get('orderId')
                
                # Skip duplicates
                if order_id in self.processed_order_ids:
                    print(f"   ⏭️ Skipping duplicate order: {order_id}")
                    continue
                
                # Extract the order with ALL COMPLETE details
                extracted_order = self.extract_complete_order_with_all_details(order_obj)
                if extracted_order:
                    orders.append(extracted_order)
                    self.processed_order_ids.add(order_id)
                    print(f"   ✅ Extracted order: {order_id} with {len(extracted_order.get('products', []))} products")
            
            print(f"✅ Total NEW orders extracted: {len(orders)}")
            return orders
            
        except Exception as e:
            print(f"❌ Error extracting order data: {e}")
            return []
    
    def extract_complete_order_with_all_details(self, order_obj):
        """Extract complete order data including ALL missing details"""
        try:
            metadata = order_obj['orderMetaData']
            
            # Basic order information
            order = {
                'order_id': metadata.get('orderId', 'N/A'),
                'order_date': self.convert_timestamp(metadata.get('orderDate')),
                'number_of_items': metadata.get('numberOfItems', 0),
                'order_status': 'N/A',
                'marketplace': metadata.get('orderMarketPlaceMeta', 'N/A'),
                'products': [],
                
                # COMPLETE FINANCIAL DETAILS
                'total_amount': 'N/A',
                'currency': 'INR',
                'payment_method': 'N/A',
                'payment_status': 'N/A',
                
                # COMPLETE DELIVERY DETAILS
                'delivery_address': 'N/A',
                'delivery_date': 'N/A',
                'delivery_status': 'N/A',
                'delivery_message': 'N/A',
                'estimated_delivery': 'N/A',
                'tracking_id': 'N/A',
                
                # ADDITIONAL ORDER DETAILS
                'order_type': 'N/A',
                'seller_details': [],
                'offers_applied': [],
                'savings_amount': 'N/A',
                'shipping_charges': 'N/A',
                'tax_details': 'N/A',
                
                # METADATA
                'extracted_at': datetime.now().isoformat(),
                'api_version': 'complete_details_v1'
            }
            
            # Extract COMPLETE financial information from orderMoneyDataBag
            if 'orderMoneyDataBag' in order_obj:
                money_data = order_obj['orderMoneyDataBag']
                order.update(self.extract_complete_financial_details(money_data))
            
            # Get product data bag for product details
            product_data_bag = order_obj.get('productDataBag', {})
            
            # Extract products from units with COMPLETE details
            if 'units' in order_obj and isinstance(order_obj['units'], dict):
                units_dict = order_obj['units']
                print(f"     🛍️ Processing {len(units_dict)} units for order {order['order_id']}")
                
                for unit_id, unit_data in units_dict.items():
                    product = self.extract_complete_product_with_all_details(unit_data, unit_id, product_data_bag)
                    if product:
                        order['products'].append(product)
            
            # Extract COMPLETE delivery information from unitProperties
            if 'unitProperties' in order_obj:
                delivery_info = self.extract_complete_delivery_details(order_obj['unitProperties'])
                order.update(delivery_info)
            
            # Extract order status from first product or unit data
            if order['products']:
                order['order_status'] = order['products'][0].get('status', 'N/A')
                order['delivery_date'] = order['products'][0].get('delivery_date', 'N/A')
                order['tracking_id'] = order['products'][0].get('tracking_id', 'N/A')
            
            # Extract seller details
            if 'sellerDataBag' in order_obj:
                order['seller_details'] = self.extract_seller_details(order_obj['sellerDataBag'])
            
            return order
            
        except Exception as e:
            print(f"⚠️ Error extracting complete order: {e}")
            return None
    
    def extract_complete_financial_details(self, money_data):
        """Extract ALL financial details from orderMoneyDataBag"""
        financial_details = {}
        
        try:
            if isinstance(money_data, dict):
                # Total amount
                if 'amount' in money_data:
                    financial_details['total_amount'] = f"₹{money_data['amount']}"
                
                # Currency details
                if 'amountValueMultiCurrency' in money_data and isinstance(money_data['amountValueMultiCurrency'], list):
                    for currency_info in money_data['amountValueMultiCurrency']:
                        if isinstance(currency_info, dict) and 'currency' in currency_info:
                            financial_details['currency'] = currency_info['currency']
                            break
                
                # Payment methods
                if 'paymentMethods' in money_data and isinstance(money_data['paymentMethods'], list):
                    financial_details['payment_method'] = ', '.join(money_data['paymentMethods'])
                
                # Savings
                if 'totalSavings' in money_data:
                    financial_details['savings_amount'] = f"₹{money_data['totalSavings']}"
                
                # Offers
                if 'offerDetails' in money_data and isinstance(money_data['offerDetails'], list):
                    offers = []
                    for offer in money_data['offerDetails']:
                        if isinstance(offer, dict) and 'offerText' in offer:
                            offers.append(offer['offerText'])
                    financial_details['offers_applied'] = offers
                
                # Payment transactions
                if 'paymentTransactions' in money_data and isinstance(money_data['paymentTransactions'], list):
                    if money_data['paymentTransactions']:
                        financial_details['payment_status'] = 'Completed'
                    else:
                        financial_details['payment_status'] = 'Pending'
        
        except Exception as e:
            print(f"⚠️ Error extracting financial details: {e}")
        
        return financial_details
    
    def extract_complete_delivery_details(self, unit_properties):
        """Extract ALL delivery details from unitProperties"""
        delivery_details = {}
        
        try:
            if isinstance(unit_properties, list) and unit_properties:
                first_unit_prop = unit_properties[0]
                
                if isinstance(first_unit_prop, dict):
                    # Delivery message
                    if 'deliveryMessage' in first_unit_prop:
                        delivery_details['delivery_message'] = first_unit_prop['deliveryMessage']
                        
                        # Extract delivery date from message
                        delivery_msg = first_unit_prop['deliveryMessage']
                        if 'Delivered on' in delivery_msg:
                            delivery_details['delivery_date'] = delivery_msg.replace('Delivered on ', '')
                            delivery_details['delivery_status'] = 'Delivered'
                        elif 'Expected by' in delivery_msg:
                            delivery_details['estimated_delivery'] = delivery_msg.replace('Expected by ', '')
                            delivery_details['delivery_status'] = 'In Transit'
                    
                    # Additional delivery info
                    if 'moRedesignHeading' in first_unit_prop:
                        delivery_details['delivery_status_heading'] = first_unit_prop['moRedesignHeading']
        
        except Exception as e:
            print(f"⚠️ Error extracting delivery details: {e}")
        
        return delivery_details
    
    def extract_complete_product_with_all_details(self, unit_data, unit_id, product_data_bag):
        """Extract complete product information with ALL details"""
        try:
            product = {
                'unit_id': unit_id,
                'product_name': 'N/A',
                'product_image': 'N/A',
                'price': 'N/A',
                'original_price': 'N/A',
                'discount': 'N/A',
                'quantity': 1,
                'seller': 'N/A',
                'seller_id': 'N/A',
                'product_url': 'N/A',
                'status': 'N/A',
                'brand': 'N/A',
                'category': 'N/A',
                'fsn': 'N/A',
                'tracking_id': 'N/A',
                'delivery_date': 'N/A',
                'delivery_message': 'N/A',
                'return_policy': 'N/A',
                'warranty': 'N/A'
            }
            
            # Extract basic info from unit data
            if isinstance(unit_data, dict):
                # Extract from metaData
                if 'metaData' in unit_data and isinstance(unit_data['metaData'], dict):
                    metadata = unit_data['metaData']
                    
                    # Product name
                    if 'title' in metadata and metadata['title']:
                        product['product_name'] = metadata['title']
                    
                    # Get listing ID from metaData
                    listing_id = metadata.get('listingId')
                    
                    # Other metadata fields
                    if 'quantity' in metadata:
                        product['quantity'] = metadata['quantity']
                    
                    if 'sellerId' in metadata:
                        product['seller_id'] = metadata['sellerId']
                    
                    if 'fsn' in metadata:
                        product['fsn'] = metadata['fsn']
                    
                    if 'trackingId' in metadata:
                        product['tracking_id'] = metadata['trackingId']
                    
                    # Extract status from metaData
                    if 'status' in metadata and isinstance(metadata['status'], dict):
                        status_data = metadata['status']
                        if 'text' in status_data:
                            product['status'] = status_data['text']
                        elif 'key' in status_data:
                            product['status'] = status_data['key']
                else:
                    listing_id = unit_data.get('listingId')
                
                # Extract COMPLETE price information from moneyDataBag
                if 'moneyDataBag' in unit_data and isinstance(unit_data['moneyDataBag'], dict):
                    money_bag = unit_data['moneyDataBag']
                    
                    # Selling price
                    if 'itemSellingPrice' in money_bag:
                        product['price'] = f"₹{money_bag['itemSellingPrice']}"
                    elif 'sellingPrice' in money_bag:
                        product['price'] = f"₹{money_bag['sellingPrice']}"
                    
                    # Original price
                    if 'itemListingPrice' in money_bag:
                        product['original_price'] = f"₹{money_bag['itemListingPrice']}"
                    elif 'listPrice' in money_bag:
                        product['original_price'] = f"₹{money_bag['listPrice']}"
                    
                    # Calculate discount
                    if 'itemListingPrice' in money_bag and 'itemSellingPrice' in money_bag:
                        original = money_bag['itemListingPrice']
                        selling = money_bag['itemSellingPrice']
                        if original > selling:
                            discount_amount = original - selling
                            discount_percent = round((discount_amount / original) * 100, 1)
                            product['discount'] = f"₹{discount_amount} ({discount_percent}% off)"
                
                # Extract delivery information from deliveryDataBag
                if 'deliveryDataBag' in unit_data and isinstance(unit_data['deliveryDataBag'], dict):
                    delivery_bag = unit_data['deliveryDataBag']
                    
                    if 'promiseDataBag' in delivery_bag and isinstance(delivery_bag['promiseDataBag'], dict):
                        promise_data = delivery_bag['promiseDataBag']
                        
                        if 'deliveryMessage' in promise_data:
                            product['delivery_message'] = promise_data['deliveryMessage']
                            
                            # Extract delivery date from message
                            delivery_msg = promise_data['deliveryMessage']
                            if 'Delivered on' in delivery_msg:
                                product['delivery_date'] = delivery_msg.replace('Delivered on ', '')
                
                # Get detailed product info from productDataBag using listingId
                if listing_id and listing_id in product_data_bag:
                    product_details = product_data_bag[listing_id]
                    
                    # Extract additional product details
                    if 'productBasicData' in product_details:
                        basic_data = product_details['productBasicData']
                        
                        # If we didn't get title from unit data, try from productBasicData
                        if product['product_name'] == 'N/A' and 'title' in basic_data:
                            product['product_name'] = basic_data['title']
                        
                        # Product URL
                        if 'url' in basic_data:
                            product['product_url'] = f"https://www.flipkart.com{basic_data['url']}"
                        
                        # Product image
                        if 'imageLocation' in basic_data and isinstance(basic_data['imageLocation'], dict):
                            # Get the best quality image
                            image_sizes = ['400x400', '200x200', '125x125', '100x100']
                            for size in image_sizes:
                                if size in basic_data['imageLocation']:
                                    image_url = basic_data['imageLocation'][size]
                                    # Replace placeholders with actual values
                                    image_url = image_url.replace('{@width}', '400').replace('{@height}', '400').replace('{@quality}', '70')
                                    product['product_image'] = image_url
                                    break
                        
                        # Category
                        if 'category' in basic_data:
                            product['category'] = basic_data['category']
                        
                        # Vertical (additional category info)
                        if 'vertical' in basic_data:
                            if product['category'] != 'N/A':
                                product['category'] = f"{product['category']} > {basic_data['vertical']}"
                            else:
                                product['category'] = basic_data['vertical']
                    
                    # Extract brand and other attributes
                    if 'productAttribute' in product_details:
                        attr_data = product_details['productAttribute']
                        
                        if 'brand' in attr_data and attr_data['brand']:
                            product['brand'] = attr_data['brand']
                        
                        # Add color and size info to product name if available
                        if 'color' in attr_data and attr_data['color']:
                            if product['product_name'] != 'N/A':
                                product['product_name'] += f" ({attr_data['color']})"
                        
                        if 'size' in attr_data and attr_data['size']:
                            if product['product_name'] != 'N/A':
                                product['product_name'] += f" - {attr_data['size']}"
            
            return product
            
        except Exception as e:
            print(f"⚠️ Error extracting complete product from unit {unit_id}: {e}")
            return None
    
    def extract_seller_details(self, seller_data_bag):
        """Extract seller details"""
        try:
            if isinstance(seller_data_bag, dict) and 'sellerDetails' in seller_data_bag:
                return seller_data_bag['sellerDetails']
            return {}
        except:
            return {}
    
    def convert_timestamp(self, timestamp):
        """Convert timestamp to readable date"""
        try:
            if timestamp:
                timestamp_seconds = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp_seconds)
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            return 'N/A'
        except:
            return 'N/A'
    
    def get_next_page_timestamp(self, current_page_data):
        """Get timestamp for next page"""
        try:
            if not isinstance(current_page_data, dict) or 'RESPONSE' not in current_page_data:
                return None
            
            response_data = current_page_data['RESPONSE']
            if 'multipleOrderDetailsView' not in response_data:
                return None
            
            order_view = response_data['multipleOrderDetailsView']
            if 'orders' not in order_view or not isinstance(order_view['orders'], list):
                return None
            
            orders_list = order_view['orders']
            if not orders_list:
                return None
            
            # Get the oldest order timestamp
            min_timestamp = None
            for order_obj in orders_list:
                if isinstance(order_obj, dict) and 'orderMetaData' in order_obj:
                    metadata = order_obj['orderMetaData']
                    order_date = metadata.get('orderDate')
                    if order_date:
                        if min_timestamp is None or order_date < min_timestamp:
                            min_timestamp = order_date
            
            return min_timestamp
        except Exception as e:
            print(f"⚠️ Error getting next page timestamp: {e}")
            return None
    
    async def save_orders_json(self, orders):
        """Save orders to JSON file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"flipkart_complete_details_orders_{timestamp}.json"
            
            # Create summary
            summary = {
                "scraping_info": {
                    "total_orders": len(orders),
                    "total_products": sum(len(order.get('products', [])) for order in orders),
                    "scraped_at": datetime.now().isoformat(),
                    "scraping_duration_seconds": round(time.time() - self.scraping_start_time, 2) if self.scraping_start_time else 0,
                    "scraping_method": "complete_details_api_with_all_information",
                    "api_endpoint": self.base_api_url,
                    "average_time_per_order": round((time.time() - self.scraping_start_time) / len(orders), 4) if self.scraping_start_time and orders else 0,
                    "unique_orders_processed": len(self.processed_order_ids),
                    "extracted_fields": [
                        "order_id", "order_date", "total_amount", "payment_method", "delivery_date",
                        "delivery_status", "tracking_id", "product_names", "product_images", "product_urls",
                        "brands", "categories", "prices", "discounts", "seller_details", "offers_applied"
                    ]
                },
                "orders": orders
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Orders saved to: {filename}")
            return filename
        except Exception as e:
            print(f"❌ Error saving orders: {e}")
            return None
    
    async def run_complete_details_scraping(self, max_pages=None):
        """Main complete details API scraping function"""
        try:
            self.scraping_start_time = time.time()
            
            print("🚀 Starting COMPLETE DETAILS API Scraping")
            print("=" * 60)
            print("🔧 Extracting ALL order details: delivery dates, tracking, prices, discounts, etc.")
            
            # Load API template
            if not await self.load_api_template():
                return False
            
            # Get cookies and headers
            cookies = await self.get_current_cookies()
            headers = await self.get_exact_headers()
            
            # Create session
            connector = aiohttp.TCPConnector(ssl=False)
            self.session = aiohttp.ClientSession(
                headers=headers,
                cookies=cookies,
                connector=connector
            )
            
            print("📡 Starting complete details API requests...")
            
            all_orders = []
            page = 1
            next_timestamp = None
            
            while True:
                if max_pages and page > max_pages:
                    print(f"🛑 Reached page limit: {max_pages}")
                    break
                
                # Fetch page data
                page_data = await self.fetch_orders_page(page, next_timestamp)
                if not page_data:
                    print(f"❌ Failed to fetch page {page}")
                    break
                
                # Extract orders
                page_orders = self.extract_order_data_from_api(page_data)
                if not page_orders:
                    print(f"📄 No NEW orders found on page {page}")
                    break
                
                all_orders.extend(page_orders)
                print(f"📦 Page {page}: Found {len(page_orders)} NEW orders (Total unique: {len(all_orders)})")
                
                # Get next page timestamp
                next_timestamp = self.get_next_page_timestamp(page_data)
                if not next_timestamp:
                    print("📄 No more pages available")
                    break
                
                page += 1
                
                # Progress update
                if page % 3 == 0:
                    elapsed = time.time() - self.scraping_start_time
                    rate = len(all_orders) / elapsed if elapsed > 0 else 0
                    print(f"⏱️ Progress: {len(all_orders)} unique orders in {elapsed:.1f}s ({rate:.1f} orders/sec)")
                
                await asyncio.sleep(0.5)
            
            # Save results
            if all_orders:
                filename = await self.save_orders_json(all_orders)
                
                # Print summary
                actual_time = round(time.time() - self.scraping_start_time, 2)
                total_products = sum(len(order.get('products', [])) for order in all_orders)
                
                print(f"\n🎉 COMPLETE DETAILS API scraping completed successfully!")
                print(f"📊 Total UNIQUE orders scraped: {len(all_orders)}")
                print(f"🛍️ Total products found: {total_products}")
                print(f"📄 Pages processed: {page - 1}")
                print(f"⏱️ Total time: {actual_time:.1f} seconds")
                print(f"⚡ Speed: {len(all_orders)/actual_time:.1f} orders/second")
                print(f"💾 Data saved to: {filename}")
                
                # Show sample with COMPLETE data
                if all_orders:
                    sample = all_orders[0]
                    print(f"\n📋 Sample order with ALL COMPLETE details:")
                    print(f"   Order ID: {sample['order_id']}")
                    print(f"   Date: {sample['order_date']}")
                    print(f"   Total Amount: {sample['total_amount']}")
                    print(f"   Payment: {sample['payment_method']}")
                    print(f"   Delivery Date: {sample['delivery_date']}")
                    print(f"   Delivery Status: {sample['delivery_status']}")
                    print(f"   Tracking ID: {sample['tracking_id']}")
                    print(f"   Savings: {sample['savings_amount']}")
                    print(f"   Products: {len(sample.get('products', []))}")
                    if sample.get('products'):
                        first_product = sample['products'][0]
                        print(f"   First product: {first_product.get('product_name', 'N/A')}")
                        print(f"   Price: {first_product.get('price', 'N/A')}")
                        print(f"   Original Price: {first_product.get('original_price', 'N/A')}")
                        print(f"   Discount: {first_product.get('discount', 'N/A')}")
                        print(f"   Brand: {first_product.get('brand', 'N/A')}")
                        print(f"   Delivery: {first_product.get('delivery_date', 'N/A')}")
                
                return True
            else:
                print("❌ No orders extracted")
                return False
                
        except Exception as e:
            print(f"❌ Error during complete details API scraping: {e}")
            return False
        
        finally:
            if self.session:
                await self.session.close()

async def main():
    print("🔧 Flipkart COMPLETE DETAILS API Scraper")
    print("🎯 Extracts ALL order details: delivery dates, tracking, prices, discounts, etc.")
    print("=" * 80)
    
    try:
        choice = input("How many pages to scrape? (Enter number or 'all'): ").strip()
        
        max_pages = None
        if choice.lower() != 'all':
            try:
                max_pages = int(choice)
            except:
                max_pages = 5
                print(f"Invalid input, using {max_pages} pages for testing")
        
        scraper = FlipkartCompleteDetailsScraper()
        success = await scraper.run_complete_details_scraping(max_pages)
        
        if success:
            print("\n✅ COMPLETE DETAILS API scraping finished successfully!")
            print("📁 Check the JSON file for ALL order and product details!")
            print("🎯 This includes: delivery dates, tracking IDs, prices, discounts, and more!")
        else:
            print("\n❌ COMPLETE DETAILS API scraping failed!")
            
    except KeyboardInterrupt:
        print("\n❌ Scraping cancelled by user")

if __name__ == "__main__":
    asyncio.run(main())