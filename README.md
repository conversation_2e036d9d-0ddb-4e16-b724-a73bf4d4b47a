# Flipkart Complete Automation

This project automates Flipkart login and orders scraping with cookie persistence and comprehensive data extraction.

## Features

### Login Automation
- 🔐 Manual login support for first-time setup
- 🍪 Cookie persistence for automatic future logins
- 🚀 Fast execution using Playwright
- 🔄 Automatic login status detection
- ⚡ Smart retry mechanism

### Orders Scraping
- 🛒 Automatic navigation to orders page
- 📄 **Pagination handling** - processes all order pages
- 🔍 **Individual order page scraping** - visits each order detail page
- 📦 **Comprehensive data extraction** - complete order information
- 🛍️ **Product-level details** - individual product information
- 💾 **Structured JSON export** - organized, detailed data
- ⏱️ **Time estimation and tracking** - progress monitoring
- 🔄 **Robust element detection** - multiple fallback methods
- 📊 **Detailed scraping statistics** - comprehensive reporting

## Setup

1. **Install dependencies:**
   ```bash
   python setup.py
   ```

   Or manually:
   ```bash
   pip install -r requirements.txt
   playwright install chromium
   ```

## Usage

### Option 1: Final Complete Scraper (🏆 RECOMMENDED)
```bash
python flipkart_final_orders_scraper.py
```
**This is the best option!** Handles login + extracts ALL order data in perfect JSON format.

### Option 2: Working Orders Scraper
```bash
python flipkart_working_orders_scraper.py
```
Extracts order data in JSON format (limited to 10 orders for testing).

### Option 2: Quick Surface-Level Scraping
```bash
python flipkart_complete_automation.py
```
This will handle login + basic orders scraping from list page only.

### Option 2: Step by Step

1. **Login first:**
   ```bash
   python flipkart_login.py
   ```

2. **Then scrape orders:**
   ```bash
   python run_orders_scraping.py
   ```

### Option 3: Detailed Orders scraping only (if already logged in)
```bash
python flipkart_detailed_orders_scraper.py
```

### Option 4: Quick orders scraping only (if already logged in)
```bash
python flipkart_orders_scraper.py
```

## First Time Setup

1. **Run complete automation:**
   - Browser opens to Flipkart login page
   - Manually enter your mobile number
   - Click "Request OTP"
   - Enter the OTP you receive
   - Complete the login process
   - Script automatically navigates to orders page
   - Extracts all order details
   - Saves data to JSON file

2. **Subsequent runs:**
   - Automatic login using saved cookies
   - Direct navigation to orders scraping
   - Fast execution without manual intervention

## How it works

1. **Cookie Management:**
   - Cookies are saved in `flipkart_cookies.json`
   - Automatically loaded on subsequent runs

2. **Login Detection:**
   - Checks for user menu elements
   - Verifies login status before proceeding

3. **Fallback Mechanism:**
   - If auto-login fails, prompts for manual login
   - Saves new cookies after successful manual login

## Extracted Order Data

### Detailed Scraping (Recommended)
Each order includes comprehensive information:

**Order Summary:**
- 📋 Order ID and URL
- 📅 Order and delivery dates
- 💰 Total amount and payment method
- 📦 Order status and delivery address
- 🏠 Billing address

**Product Details (for each product):**
- 🛍️ Product name and image URL
- 💰 Individual product price
- 📊 Quantity ordered
- 🏪 Seller information
- 📋 Product specifications
- 🚚 Delivery information

**Tracking Information:**
- 📦 Current status
- 🔢 Tracking ID
- 📅 Estimated delivery
- 📈 Tracking history timeline

**Metadata:**
- ⏰ Extraction timestamp
- 🔗 Source URLs
- 📊 Scraping statistics

## Files

### Main Scripts
- `flipkart_final_orders_scraper.py` - **🏆 FINAL COMPLETE SCRAPER (RECOMMENDED)**
- `flipkart_working_orders_scraper.py` - **Working JSON orders scraper**
- `flipkart_complete_detailed_automation.py` - Complete detailed automation
- `flipkart_detailed_orders_scraper.py` - Detailed orders scraping only
- `flipkart_complete_automation.py` - Quick complete automation (login + basic scraping)
- `flipkart_login.py` - Login automation only
- `flipkart_orders_scraper.py` - Basic orders scraping only
- `run_orders_scraping.py` - Simple orders scraper

### Debug/Test Scripts
- `debug_orders_page.py` - Debug orders page structure
- `test_single_order.py` - Test single order extraction

### Generated Files
- `flipkart_cookies.json` - Saved login cookies
- `flipkart_orders_YYYYMMDD_HHMMSS.json` - Scraped orders data

### Setup Files
- `requirements.txt` - Python dependencies
- `setup.py` - Setup script
- `README.md` - This documentation

## Troubleshooting

- **Login timeout:** Increase the `max_wait_time` in the script
- **Cookies not working:** Delete `flipkart_cookies.json` and login manually again
- **Browser issues:** Make sure Playwright browsers are installed: `playwright install chromium`

## Security Note

- Keep your `flipkart_cookies.json` file secure
- Don't share cookies with others
- Cookies may expire and require re-login